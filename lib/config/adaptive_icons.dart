import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'platform_adaptations.dart';

/// Platform-specific icon system for Dasso Reader
///
/// Provides platform-appropriate icons that enhance the native feel
/// while maintaining consistency with the app's design system.
class AdaptiveIcons {
  // =====================================================
  // NAVIGATION ICONS
  // =====================================================

  /// Platform-appropriate back icon
  static IconData get back {
    // Use chevron style back button like in profile header navigation
    return Icons.chevron_left;
  }

  /// Platform-appropriate forward icon
  static IconData get forward {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.forward;
    }
    return Icons.arrow_forward;
  }

  /// Platform-appropriate close icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get close => Icons.close;

  /// Platform-appropriate menu icon
  static IconData get menu {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.line_horizontal_3;
    }
    return Icons.menu;
  }

  /// Platform-appropriate more options icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get moreOptions => Icons.more_vert;

  // =====================================================
  // ACTION ICONS
  // =====================================================

  /// Platform-appropriate add icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get add => Icons.add;

  /// Platform-appropriate delete icon
  static IconData get delete {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.delete;
    }
    return Icons.delete_outline;
  }

  /// Platform-appropriate edit icon
  static IconData get edit {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.pencil;
    }
    return Icons.edit_outlined;
  }

  /// Platform-appropriate save icon
  static IconData get save {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark;
    }
    return Icons.save_outlined;
  }

  /// Platform-appropriate share icon
  static IconData get share {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.share;
    }
    return Icons.share_outlined;
  }

  /// Platform-appropriate copy icon
  static IconData get copy {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_on_doc;
    }
    return Icons.copy_outlined;
  }

  // =====================================================
  // CONTENT ICONS
  // =====================================================

  /// Platform-appropriate search icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get search => Icons.search;

  /// Platform-appropriate book icon
  static IconData get book {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book;
    }
    return Icons.menu_book_outlined;
  }

  /// Platform-appropriate bookmark icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get bookmark => Icons.bookmark_outline;

  /// Platform-appropriate bookmark filled icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get bookmarkFilled => Icons.bookmark;

  /// Platform-appropriate note icon
  static IconData get note {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text;
    }
    return Icons.note_outlined;
  }

  // =====================================================
  // MEDIA ICONS
  // =====================================================

  /// Platform-appropriate play icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get play => Icons.play_arrow;

  /// Platform-appropriate pause icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get pause => Icons.pause;

  /// Platform-appropriate stop icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get stop => Icons.stop;

  /// Platform-appropriate volume icon
  static IconData get volume {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.speaker_2;
    }
    return Icons.volume_up_outlined;
  }

  // =====================================================
  // SETTINGS ICONS
  // =====================================================

  /// Platform-appropriate settings icon
  static IconData get settings {
    // Use Material icons for both platforms as they work consistently
    return Icons.settings_outlined;
  }

  /// Platform-appropriate profile icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get profile => Icons.person_outline;

  /// Platform-appropriate info icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get info => Icons.info_outline;

  // =====================================================
  // STATUS ICONS
  // =====================================================

  /// Platform-appropriate success icon
  static IconData get success {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark_circle_fill;
    }
    return Icons.check_circle;
  }

  /// Platform-appropriate error icon
  static IconData get error {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.exclamationmark_circle_fill;
    }
    return Icons.error;
  }

  /// Platform-appropriate warning icon
  static IconData get warning {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.exclamationmark_triangle_fill;
    }
    return Icons.warning;
  }

  // =====================================================
  // LEARNING ICONS (Chinese Learning Specific)
  // =====================================================

  /// Platform-appropriate bookshelf icon
  /// Using Material Design for semantic consistency across platforms
  static IconData get bookshelf => Icons.library_books_outlined;

  /// Platform-appropriate bookshelf filled icon
  /// Using Material Design for semantic consistency across platforms
  static IconData get bookshelfFilled => Icons.library_books;

  /// Platform-appropriate dictionary icon
  /// Using Material Design for clear semantic meaning
  static IconData get dictionary => Icons.translate_outlined;

  /// Platform-appropriate dictionary filled icon
  /// Using Material Design for clear semantic meaning
  static IconData get dictionaryFilled => Icons.translate;

  /// Platform-appropriate vocabulary icon
  /// Using Material Design for semantic consistency
  static IconData get vocabulary => Icons.menu_book_outlined;

  /// Platform-appropriate vocabulary filled icon
  /// Using Material Design for semantic consistency
  static IconData get vocabularyFilled => Icons.menu_book;

  /// Platform-appropriate HSK icon
  /// Using Material Design for educational context clarity
  static IconData get hsk => Icons.school_outlined;

  /// Platform-appropriate HSK filled icon
  /// Using Material Design for educational context clarity
  static IconData get hskFilled => Icons.school;

  /// Platform-appropriate notes icon
  /// Using Material Design for document context clarity
  static IconData get notes => Icons.note_outlined;

  /// Platform-appropriate notes filled icon
  /// Using Material Design for document context clarity
  static IconData get notesFilled => Icons.note;

  /// Platform-appropriate audio icon
  static IconData get audio {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.speaker_1;
    }
    return Icons.volume_up;
  }

  // =====================================================
  // ADDITIONAL ADAPTIVE ICONS
  // =====================================================

  /// Platform-appropriate text fields icon
  static IconData get textFields {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat;
    }
    return Icons.text_fields;
  }

  /// Platform-appropriate text fields outlined icon
  static IconData get textFieldsOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat;
    }
    return Icons.text_fields_outlined;
  }

  /// Platform-appropriate content cut icon (for segmentation)
  static IconData get contentCut {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.scissors;
    }
    return Icons.content_cut;
  }

  /// Platform-appropriate help outline icon
  static IconData get helpOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.question_circle;
    }
    return Icons.help_outline;
  }

  /// Platform-appropriate auto fix high outlined icon
  static IconData get autoFixHighOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.wand_stars;
    }
    return Icons.auto_fix_high_outlined;
  }

  /// Platform-appropriate edit note rounded icon
  static IconData get editNoteRounded {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.pencil_outline;
    }
    return Icons.edit_note_rounded;
  }

  /// Platform-appropriate file download outlined icon
  static IconData get fileDownloadOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.cloud_download;
    }
    return Icons.file_download_outlined;
  }

  /// Platform-appropriate iOS share icon
  static IconData get iosShare {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.share;
    }
    return Icons.ios_share;
  }

  /// Platform-appropriate menu book rounded icon
  static IconData get menuBookRounded {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book_fill;
    }
    return Icons.menu_book_rounded;
  }

  /// Platform-appropriate delete outline icon
  static IconData get deleteOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.delete;
    }
    return Icons.delete_outline;
  }

  /// Platform-appropriate search rounded icon
  static IconData get searchRounded {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.search;
    }
    return Icons.search_rounded;
  }

  /// Platform-appropriate clear icon
  static IconData get clear {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.clear;
    }
    return Icons.clear;
  }

  /// Platform-appropriate info outline icon
  static IconData get infoOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.info_circle;
    }
    return Icons.info_outline;
  }

  /// Platform-appropriate translate icon
  static IconData get translate {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat_alt;
    }
    return Icons.translate;
  }

  /// Platform-appropriate error outline icon
  static IconData get errorOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.exclamationmark_circle;
    }
    return Icons.error_outline;
  }

  /// Platform-appropriate arrow back icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get arrowBack => Icons.arrow_back;

  /// Platform-appropriate arrow forward icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get arrowForward => Icons.arrow_forward;

  /// Platform-appropriate auto awesome icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get autoAwesome => Icons.auto_awesome;

  /// Platform-appropriate sync icon
  static IconData get sync {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.arrow_2_circlepath;
    }
    return Icons.sync;
  }

  /// Platform-appropriate cached icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get cached => Icons.cached;

  /// Platform-appropriate upload icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get upload => Icons.upload;

  /// Platform-appropriate storage icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get storage => Icons.storage;

  /// Platform-appropriate shield icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get shield => Icons.shield;

  /// Platform-appropriate arrow upward icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get arrowUpward => Icons.arrow_upward;

  /// Platform-appropriate arrow downward icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get arrowDownward => Icons.arrow_downward;

  /// Platform-appropriate filter list icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get filterList => Icons.filter_list;

  /// Platform-appropriate filter list off icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get filterListOff => Icons.filter_list_off;

  /// Platform-appropriate bar chart icon
  static IconData get barChart {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.chart_bar;
    }
    return Icons.bar_chart;
  }

  /// Platform-appropriate color lens icon
  static IconData get colorLens {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.paintbrush;
    }
    return Icons.color_lens;
  }

  /// Platform-appropriate more horizontal icon
  static IconData get moreHoriz {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.ellipsis;
    }
    return Icons.more_horiz;
  }

  /// Platform-appropriate verified icon
  static IconData get verified {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark_seal_fill;
    }
    return Icons.verified;
  }

  /// Platform-appropriate access time icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get accessTime => Icons.access_time;

  /// Platform-appropriate timer off icon
  static IconData get timerOff {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.timer;
    }
    return Icons.timer_off;
  }

  /// Platform-appropriate stars icon
  static IconData get stars {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.star_fill;
    }
    return Icons.stars;
  }

  /// Platform-appropriate refresh icon
  static IconData get refresh {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.refresh;
    }
    return Icons.refresh;
  }

  /// Platform-appropriate iOS-style back arrow
  static IconData get arrowBackIos {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.back;
    }
    return Icons.arrow_back_ios;
  }

  /// Platform-appropriate iOS-style forward arrow
  static IconData get arrowForwardIos {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.forward;
    }
    return Icons.arrow_forward_ios;
  }

  /// Platform-appropriate question mark icon
  static IconData get questionMark {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.question;
    }
    return Icons.question_mark;
  }

  /// Platform-appropriate format underline icon
  static IconData get formatUnderline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.underline;
    }
    return Icons.format_underline;
  }

  // =====================================================
  // READING AREA ICONS (CRITICAL iOS FIX)
  // =====================================================

  /// Platform-appropriate more vertical icon
  /// Using Material Design for consistency across platforms
  static IconData get moreVertical => Icons.more_vert;

  /// Platform-appropriate headphones icon
  /// Using Material Design for audio context clarity
  static IconData get headphones => Icons.headphones;

  /// Platform-appropriate format indent increase icon
  /// Using Material Design for text formatting consistency
  static IconData get formatIndentIncrease => Icons.format_indent_increase;

  /// Platform-appropriate compare arrows icon
  /// Using Material Design for spacing controls
  static IconData get compareArrows => Icons.compare_arrows;

  /// Platform-appropriate vertical align top icon
  /// Using Material Design for margin controls
  static IconData get verticalAlignTop => Icons.vertical_align_top_outlined;

  /// Platform-appropriate vertical align bottom icon
  /// Using Material Design for margin controls
  static IconData get verticalAlignBottom =>
      Icons.vertical_align_bottom_outlined;

  /// Platform-appropriate format bold icon
  /// Using Material Design for text formatting
  static IconData get formatBold => Icons.format_bold;

  /// Platform-appropriate height icon
  /// Using Material Design for spacing controls
  static IconData get height => Icons.height;

  /// Platform-appropriate download icon for reading area
  /// Using Material Design for download actions
  static IconData get downloadReading => Icons.download;

  /// Platform-appropriate add icon for reading area
  /// Using Material Design for add actions
  static IconData get addReading => Icons.add;

  /// Platform-appropriate margin rounded icon
  /// Using Material Design for layout controls
  static IconData get marginRounded => Icons.margin_rounded;

  /// Platform-appropriate format align justify icon
  /// Using Material Design for text alignment
  static IconData get formatAlignJustify => Icons.format_align_justify;

  /// Platform-appropriate line weight icon
  /// Using Material Design for line controls
  static IconData get lineWeight => Icons.line_weight;

  /// Platform-appropriate circle icon for reading area
  /// Using Material Design for color selection
  static IconData get circleReading => Icons.circle;

  /// Platform-appropriate text fields icon (alternative)
  /// Using Material Design for text controls
  static IconData get textFieldsIcon => Icons.text_fields;

  /// Platform-appropriate delete icon for reading area
  /// Using Material Design for delete actions
  static IconData get deleteReading => Icons.delete;

  /// Platform-appropriate error outline icon for reading area
  /// Using Material Design for information display
  static IconData get errorOutlineReading => Icons.error_outline;

  /// Platform-appropriate auto awesome icon for reading area
  /// Using Material Design for auto features
  static IconData get autoAwesomeReading => Icons.auto_awesome;

  /// Platform-appropriate book icon for reading area
  /// Using Material Design for book representation
  static IconData get bookReading => Icons.book;

  /// Platform-appropriate book open icon for reading area
  /// Using Material Design for open book representation (using menu_book as alternative)
  static IconData get bookOpenReading => Icons.menu_book;

  /// Platform-appropriate arrow drop down icon for reading area
  /// Using Material Design for dropdown controls
  static IconData get arrowDropDownReading => Icons.arrow_drop_down;

  /// Platform-appropriate font download outlined icon for reading area
  /// Using Material Design for font download
  static IconData get fontDownloadOutlinedReading =>
      Icons.font_download_outlined;

  /// Platform-appropriate arrow forward iOS icon for reading area
  /// Using Material Design for forward navigation
  static IconData get arrowForwardIosReading => Icons.arrow_forward_ios;

  /// Platform-appropriate note alt icon
  static IconData get noteAlt {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text;
    }
    return Icons.note_alt;
  }

  /// Platform-appropriate note alt outlined icon
  static IconData get noteAltOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text;
    }
    return Icons.note_alt_outlined;
  }

  /// Platform-appropriate brush icon
  static IconData get brush {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.paintbrush;
    }
    return Icons.brush;
  }

  /// Platform-appropriate arrow drop down icon
  static IconData get arrowDropDown {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.chevron_down;
    }
    return Icons.arrow_drop_down;
  }

  /// Platform-appropriate library books outlined icon
  static IconData get libraryBooksOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book;
    }
    return Icons.library_books_outlined;
  }

  /// Platform-appropriate search off icon
  static IconData get searchOff {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.search;
    }
    return Icons.search_off;
  }

  /// Platform-appropriate bookmark border icon
  static IconData get bookmarkBorder {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.bookmark;
    }
    return Icons.bookmark_border;
  }

  /// Platform-appropriate history icon
  static IconData get history {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.clock;
    }
    return Icons.history;
  }

  /// Platform-appropriate wifi off icon
  static IconData get wifiOff {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.wifi_slash;
    }
    return Icons.wifi_off;
  }

  /// Platform-appropriate lock outline icon
  static IconData get lockOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.lock;
    }
    return Icons.lock_outline;
  }

  /// Platform-appropriate inbox outlined icon
  static IconData get inboxOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.tray;
    }
    return Icons.inbox_outlined;
  }

  /// Platform-appropriate grid view icon
  static IconData get gridView {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.square_grid_2x2;
    }
    return Icons.grid_view;
  }

  /// Platform-appropriate circle outlined icon
  static IconData get circleOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.circle;
    }
    return Icons.circle_outlined;
  }

  /// Platform-appropriate circle icon
  static IconData get circle {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.circle_fill;
    }
    return Icons.circle;
  }

  /// Platform-appropriate table of contents icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get toc => Icons.toc;

  /// Platform-appropriate sunny outlined icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get wbSunnyOutlined => Icons.wb_sunny_outlined;

  /// Platform-appropriate auto stories outlined icon
  /// Using Material Design for consistency across platforms (iOS fix)
  static IconData get autoStoriesOutlined => Icons.auto_stories_outlined;

  /// Platform-appropriate person icon
  static IconData get person {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.person_fill;
    }
    return Icons.person;
  }

  /// Platform-appropriate image icon
  static IconData get image {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.photo;
    }
    return Icons.image;
  }

  /// Platform-appropriate image outlined icon
  static IconData get imageOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.photo;
    }
    return Icons.image_outlined;
  }

  /// Platform-appropriate image not supported icon
  static IconData get imageNotSupported {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.photo;
    }
    return Icons.image_not_supported;
  }

  /// Platform-appropriate landscape icon
  static IconData get landscape {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.photo_on_rectangle;
    }
    return Icons.landscape;
  }

  /// Platform-appropriate photo library icon
  static IconData get photoLibrary {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.photo_on_rectangle;
    }
    return Icons.photo_library;
  }

  /// Platform-appropriate wallpaper icon
  static IconData get wallpaper {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.photo_on_rectangle;
    }
    return Icons.wallpaper;
  }

  /// Platform-appropriate phone android icon
  static IconData get phoneAndroid {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.device_phone_portrait;
    }
    return Icons.phone_android;
  }

  /// Platform-appropriate smartphone icon
  static IconData get smartphone {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.device_phone_portrait;
    }
    return Icons.smartphone;
  }

  /// Platform-appropriate check circle icon
  static IconData get checkCircle {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark_circle_fill;
    }
    return Icons.check_circle;
  }

  /// Platform-appropriate tune icon
  static IconData get tune {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.slider_horizontal_3;
    }
    return Icons.tune;
  }

  /// Platform-appropriate space bar icon
  static IconData get spaceBar {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.rectangle;
    }
    return Icons.space_bar;
  }

  /// Platform-appropriate format size icon
  static IconData get formatSize {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat_size;
    }
    return Icons.format_size;
  }

  /// Platform-appropriate layers icon
  static IconData get layers {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.square_stack;
    }
    return Icons.layers;
  }

  /// Platform-appropriate star icon
  static IconData get star {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.star_fill;
    }
    return Icons.star;
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Creates platform-appropriate icon widget
  static Widget createAdaptiveIcon(
    IconData iconData, {
    double? size,
    Color? color,
  }) {
    return Icon(
      iconData,
      size: size,
      color: color,
    );
  }

  /// Creates platform-appropriate icon button
  static Widget createAdaptiveIconButton({
    required IconData icon,
    required VoidCallback onPressed,
    double? size,
    Color? color,
    String? tooltip,
  }) {
    return IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      iconSize: size,
      color: color,
      tooltip: tooltip,
    );
  }

  /// Returns platform-appropriate chevron icon for navigation
  static IconData get chevronRight {
    // Use Material icons for both platforms as they work consistently
    return Icons.chevron_right;
  }

  /// Returns platform-appropriate chevron icon for dropdowns
  static IconData get chevronDown {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.chevron_down;
    }
    return Icons.keyboard_arrow_down;
  }

  /// Returns platform-appropriate home icon
  static IconData get home {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.house;
    }
    return Icons.home_outlined;
  }
}
